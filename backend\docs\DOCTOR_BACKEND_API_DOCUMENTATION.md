# 🏥 DOCTOR BACKEND API DOCUMENTATION
## Complete Guide to All Doctor Backend Functionality

### 📋 Table of Contents
1. [System Overview](#system-overview)
2. [Authentication & Security](#authentication--security)
3. [Doctor Dashboard APIs](#doctor-dashboard-apis)
4. [Doctor Profile Management APIs](#doctor-profile-management-apis)
5. [Doctor Patients Management APIs](#doctor-patients-management-apis)
6. [Doctor Reports Management APIs](#doctor-reports-management-apis)
7. [Doctor Appointments APIs](#doctor-appointments-apis)
8. [File Management APIs](#file-management-apis)
9. [Database Models & Relationships](#database-models--relationships)
10. [Error Handling & Status Codes](#error-handling--status-codes)
11. [Implementation Examples](#implementation-examples)

---

## 🔧 System Overview

### Base Configuration
- **Base URL:** `http://localhost:5000/api`
- **Database:** MongoDB with Mongoose ODM
- **Authentication:** JWT (JSON Web Tokens)
- **File Storage:** Local filesystem (`/uploads/` directory)
- **Role System:** User roles (`doctor`, `patient`, `admin`)

### Technology Stack
- **Backend Framework:** Express.js
- **Database:** MongoDB
- **Authentication:** JWT + bcrypt
- **File Handling:** Multer middleware
- **Validation:** Custom middleware
- **CORS:** Enabled for frontend integration

---

## 🔐 Authentication & Security

### JWT Token Structure
```javascript
// Token Payload
{
  "userId": "ObjectId",
  "email": "<EMAIL>",
  "role": "doctor",
  "iat": **********,
  "exp": **********
}
```

### Authentication Headers
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

### Security Middleware Chain
1. **authMiddleware.js** - Validates JWT token
2. **Role Verification** - Ensures `user.role === 'doctor'`
3. **Data Ownership** - Verifies doctor can access specific resources
4. **Input Sanitization** - Prevents injection attacks

### Protected Route Example
```javascript
// All doctor routes use this pattern
router.get('/endpoint', authMiddleware, async (req, res) => {
  // req.user contains decoded JWT payload
  const doctorId = req.user.userId;
  const doctorEmail = req.user.email;
  // Route logic here
});
```

---

## 📊 Doctor Dashboard APIs

### Base Route: `/api/doctor/dashboard`

#### 📈 GET `/api/doctor/dashboard/stats`
**Purpose:** Comprehensive dashboard statistics for logged-in doctor

**Authentication:** Required (Doctor role)

**Request Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Response Structure:**
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalPatients": 156,
      "totalAppointments": 342,
      "totalReports": 289,
      "activePatients": 134
    },
    "appointments": {
      "today": 8,
      "thisWeek": 45,
      "pending": 12,
      "completed": 298,
      "cancelled": 32,
      "upcoming": [
        {
          "_id": "appointment_id",
          "patientName": "John Smith",
          "date": "2025-01-29T10:00:00.000Z",
          "type": "consultation",
          "status": "confirmed"
        }
      ]
    },
    "reports": {
      "pending": 23,
      "completed": 245,
      "reviewed": 21,
      "recent": [
        {
          "_id": "report_id",
          "type": "Blood Test",
          "patientName": "Jane Doe",
          "date": "2025-01-28T00:00:00.000Z",
          "status": "pending"
        }
      ]
    },
    "patients": {
      "newThisMonth": 12,
      "activeThisWeek": 67,
      "recentlyAdded": [
        {
          "_id": "patient_id",
          "name": "Alice Johnson",
          "email": "<EMAIL>",
          "addedDate": "2025-01-25T00:00:00.000Z"
        }
      ]
    },
    "performance": {
      "averageConsultationTime": "25 minutes",
      "patientSatisfactionRate": "94%",
      "reportCompletionRate": "87%"
    }
  }
}
```

**Error Responses:**
```json
// 401 Unauthorized
{
  "success": false,
  "message": "Access denied. No token provided."
}

// 403 Forbidden
{
  "success": false,
  "message": "Access denied. Doctor role required."
}

// 500 Internal Server Error
{
  "success": false,
  "message": "Error fetching dashboard statistics",
  "error": "Database connection failed"
}
```

**Implementation Details:**
- Aggregates data from multiple collections (Users, Appointments, Reports, PatientRecords)
- Uses MongoDB aggregation pipeline for efficient data processing
- Caches results for 5 minutes to improve performance
- Includes real-time calculations for current day/week/month

---

## 👤 Doctor Profile Management APIs

### Base Route: `/api/doctor/profile`

#### 📋 GET `/api/doctor/profile`
**Purpose:** Retrieve complete doctor profile information

**Authentication:** Required (Doctor role)

**Request Example:**
```http
GET /api/doctor/profile
Authorization: Bearer <jwt_token>
```

**Response Structure:**
```json
{
  "success": true,
  "data": {
    "_id": "68407db825ce90a8b1374f0d",
    "name": "Dr. Sarah Wilson",
    "email": "<EMAIL>",
    "role": "doctor",
    "profile": {
      "specialization": "Cardiology",
      "experience": "12 years",
      "education": "MD from Harvard Medical School",
      "certifications": [
        "Board Certified Cardiologist",
        "Advanced Cardiac Life Support (ACLS)"
      ],
      "phone": "******-0123",
      "address": {
        "street": "123 Medical Center Dr",
        "city": "Boston",
        "state": "MA",
        "zipCode": "02115"
      },
      "workingHours": {
        "monday": "9:00 AM - 5:00 PM",
        "tuesday": "9:00 AM - 5:00 PM",
        "wednesday": "9:00 AM - 5:00 PM",
        "thursday": "9:00 AM - 5:00 PM",
        "friday": "9:00 AM - 3:00 PM",
        "saturday": "Closed",
        "sunday": "Closed"
      },
      "languages": ["English", "Spanish", "French"],
      "profileImage": "/uploads/profiles/doctor_12345.jpg"
    },
    "statistics": {
      "totalPatients": 156,
      "totalConsultations": 1247,
      "yearsOfService": 12,
      "averageRating": 4.8
    },
    "createdAt": "2023-01-15T08:30:00.000Z",
    "updatedAt": "2025-01-28T14:22:00.000Z"
  }
}
```

#### ✏️ PUT `/api/doctor/profile`
**Purpose:** Update doctor profile information

**Authentication:** Required (Doctor role)

**Request Headers:**
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Dr. Sarah Wilson",
  "profile": {
    "specialization": "Interventional Cardiology",
    "experience": "13 years",
    "education": "MD from Harvard Medical School, Fellowship in Interventional Cardiology",
    "certifications": [
      "Board Certified Cardiologist",
      "Advanced Cardiac Life Support (ACLS)",
      "Interventional Cardiology Certification"
    ],
    "phone": "******-0123",
    "address": {
      "street": "123 Medical Center Dr",
      "city": "Boston",
      "state": "MA",
      "zipCode": "02115"
    },
    "workingHours": {
      "monday": "8:00 AM - 6:00 PM",
      "tuesday": "8:00 AM - 6:00 PM",
      "wednesday": "8:00 AM - 6:00 PM",
      "thursday": "8:00 AM - 6:00 PM",
      "friday": "8:00 AM - 4:00 PM",
      "saturday": "9:00 AM - 1:00 PM",
      "sunday": "Closed"
    },
    "languages": ["English", "Spanish", "French", "Italian"]
  }
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "_id": "68407db825ce90a8b1374f0d",
    "name": "Dr. Sarah Wilson",
    "email": "<EMAIL>",
    "profile": {
      // Updated profile data
    },
    "updatedAt": "2025-01-29T10:15:00.000Z"
  }
}
```

**Validation Rules:**
- `name`: Required, 2-100 characters
- `specialization`: Required, 2-100 characters
- `experience`: Required, format: "X years" or "X months"
- `phone`: Optional, valid phone number format
- `email`: Cannot be changed through this endpoint
- `workingHours`: Valid time format (HH:MM AM/PM)

---

## 👥 Doctor Patients Management APIs

### Base Route: `/api/doctor/patients`

#### 📋 GET `/api/doctor/patients`
**Purpose:** Retrieve all patients associated with the logged-in doctor

**Authentication:** Required (Doctor role)

**Query Parameters:**
```http
GET /api/doctor/patients?search=john&page=1&limit=10&sortBy=name&sortOrder=asc&gender=male&ageRange=30-50
```

**Available Query Parameters:**
- `search` (string): Search by patient name, email, or phone
- `page` (number): Page number for pagination (default: 1)
- `limit` (number): Items per page (default: 10, max: 100)
- `sortBy` (string): Sort field (name, email, age, createdAt)
- `sortOrder` (string): Sort direction (asc, desc)
- `gender` (string): Filter by gender (male, female, other)
- `ageRange` (string): Age range filter (e.g., "20-30", "40-60")
- `status` (string): Patient status (active, inactive)

**Response Structure:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "6821f5111c121eb2864f1d61",
      "name": "MUHAMMAD ABDULLAH CHEEMA",
      "email": "<EMAIL>",
      "phone": "+92-***********",
      "age": 40,
      "gender": "male",
      "address": {
        "street": "123 Main Street",
        "city": "Lahore",
        "state": "Punjab",
        "zipCode": "54000",
        "country": "Pakistan"
      },
      "medicalInfo": {
        "bloodType": "O+",
        "allergies": ["Penicillin", "Shellfish"],
        "chronicConditions": ["Hypertension"],
        "emergencyContact": {
          "name": "Fatima Cheema",
          "relationship": "Wife",
          "phone": "+92-***********"
        }
      },
      "insurance": {
        "provider": "State Life Insurance",
        "policyNumber": "SLI-123456789",
        "groupNumber": "GRP-001"
      },
      "statistics": {
        "totalAppointments": 15,
        "totalReports": 8,
        "lastVisit": "2025-01-25T10:30:00.000Z",
        "nextAppointment": "2025-02-05T14:00:00.000Z"
      },
      "status": "active",
      "createdAt": "2024-12-15T08:00:00.000Z",
      "updatedAt": "2025-01-28T16:45:00.000Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 16,
    "totalPatients": 156,
    "hasNext": true,
    "hasPrev": false,
    "limit": 10
  },
  "filters": {
    "search": "john",
    "gender": "male",
    "ageRange": "30-50",
    "appliedFilters": 3
  }
}
```

#### 👤 GET `/api/doctor/patients/:patientId`
**Purpose:** Get detailed information about a specific patient

**Authentication:** Required (Doctor role)

**Request Example:**
```http
GET /api/doctor/patients/6821f5111c121eb2864f1d61
Authorization: Bearer <jwt_token>
```

**Response Structure:**
```json
{
  "success": true,
  "data": {
    "_id": "6821f5111c121eb2864f1d61",
    "name": "MUHAMMAD ABDULLAH CHEEMA",
    "email": "<EMAIL>",
    "phone": "+92-***********",
    "age": 40,
    "gender": "male",
    "dateOfBirth": "1984-03-15T00:00:00.000Z",
    "address": {
      "street": "123 Main Street",
      "city": "Lahore",
      "state": "Punjab",
      "zipCode": "54000",
      "country": "Pakistan"
    },
    "medicalHistory": {
      "bloodType": "O+",
      "height": "175 cm",
      "weight": "75 kg",
      "allergies": ["Penicillin", "Shellfish"],
      "chronicConditions": ["Hypertension", "Type 2 Diabetes"],
      "medications": [
        {
          "name": "Lisinopril",
          "dosage": "10mg",
          "frequency": "Once daily",
          "startDate": "2023-06-15T00:00:00.000Z"
        }
      ],
      "surgeries": [
        {
          "procedure": "Appendectomy",
          "date": "2019-08-22T00:00:00.000Z",
          "hospital": "Lahore General Hospital"
        }
      ]
    },
    "emergencyContact": {
      "name": "Fatima Cheema",
      "relationship": "Wife",
      "phone": "+92-***********",
      "email": "<EMAIL>"
    },
    "insurance": {
      "provider": "State Life Insurance",
      "policyNumber": "SLI-123456789",
      "groupNumber": "GRP-001",
      "expiryDate": "2025-12-31T00:00:00.000Z"
    },
    "appointments": [
      {
        "_id": "appointment_id",
        "date": "2025-02-05T14:00:00.000Z",
        "type": "Follow-up",
        "status": "scheduled",
        "notes": "Blood pressure check"
      }
    ],
    "reports": [
      {
        "_id": "6821f5251c121eb2864f1d67",
        "type": "EKG/ECG",
        "date": "2025-01-25T00:00:00.000Z",
        "status": "pending",
        "pdfPath": "/uploads/report_1747055909855.pdf"
      }
    ],
    "vitalSigns": {
      "lastRecorded": "2025-01-25T10:30:00.000Z",
      "bloodPressure": "130/85 mmHg",
      "heartRate": "72 bpm",
      "temperature": "98.6°F",
      "weight": "75 kg",
      "height": "175 cm"
    },
    "statistics": {
      "totalAppointments": 15,
      "completedAppointments": 12,
      "cancelledAppointments": 2,
      "totalReports": 8,
      "lastVisit": "2025-01-25T10:30:00.000Z",
      "patientSince": "2023-03-10T00:00:00.000Z"
    },
    "status": "active",
    "createdAt": "2024-12-15T08:00:00.000Z",
    "updatedAt": "2025-01-28T16:45:00.000Z"
  }
}
```

**Error Responses:**
```json
// 404 Not Found
{
  "success": false,
  "message": "Patient not found or access denied"
}

// 400 Bad Request
{
  "success": false,
  "message": "Invalid patient ID format"
}
```

#### 📝 POST `/api/doctor/patients`
**Purpose:** Add a new patient to the doctor's patient list

**Authentication:** Required (Doctor role)

**Request Body:**
```json
{
  "name": "John Smith",
  "email": "<EMAIL>",
  "phone": "******-0123",
  "age": 35,
  "gender": "male",
  "dateOfBirth": "1989-05-15",
  "address": {
    "street": "456 Oak Avenue",
    "city": "New York",
    "state": "NY",
    "zipCode": "10001",
    "country": "USA"
  },
  "medicalHistory": {
    "bloodType": "A+",
    "height": "180 cm",
    "weight": "80 kg",
    "allergies": ["Peanuts"],
    "chronicConditions": [],
    "medications": []
  },
  "emergencyContact": {
    "name": "Jane Smith",
    "relationship": "Wife",
    "phone": "******-0124",
    "email": "<EMAIL>"
  },
  "insurance": {
    "provider": "Blue Cross Blue Shield",
    "policyNumber": "BCBS-987654321",
    "groupNumber": "GRP-002"
  }
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Patient added successfully",
  "data": {
    "_id": "new_patient_id",
    "name": "John Smith",
    "email": "<EMAIL>",
    // ... complete patient data
    "createdAt": "2025-01-29T10:30:00.000Z"
  }
}
```

#### ✏️ PUT `/api/doctor/patients/:patientId`
**Purpose:** Update patient information

**Authentication:** Required (Doctor role)

**Request Body:** (Same structure as POST, all fields optional)

**Success Response:**
```json
{
  "success": true,
  "message": "Patient updated successfully",
  "data": {
    // Updated patient data
  }
}
```

---

## 📊 Doctor Reports Management APIs

### Base Route: `/api/doctor/reports`

#### 📋 GET `/api/doctor/reports`
**Purpose:** Get all medical reports created by the logged-in doctor with advanced filtering

**Authentication:** Required (Doctor role)

**Query Parameters:**
```http
GET /api/doctor/reports?search=blood&patientId=123&status=pending&type=Blood Test&page=1&limit=20&sortBy=date&sortOrder=desc&dateFrom=2025-01-01&dateTo=2025-01-31
```

**Available Query Parameters:**
- `search` (string): Search across report type, patient name, patient email
- `patientId` (ObjectId): Filter reports for specific patient
- `status` (string): Filter by status (pending, completed, reviewed, cancelled)
- `type` (string): Filter by report type (Blood Test, X-Ray, MRI, etc.)
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10, max: 100)
- `sortBy` (string): Sort field (date, type, status, patientName)
- `sortOrder` (string): Sort direction (asc, desc, default: desc)
- `dateFrom` (date): Filter reports from this date (YYYY-MM-DD)
- `dateTo` (date): Filter reports until this date (YYYY-MM-DD)

**Response Structure:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "6821f5251c121eb2864f1d67",
      "type": "EKG/ECG",
      "date": "2025-01-25T00:00:00.000Z",
      "status": "pending",
      "description": "Routine cardiac evaluation for chest pain symptoms",
      "findings": "Normal sinus rhythm, no acute abnormalities detected",
      "recommendations": "Continue current medications, follow-up in 3 months",
      "pdfPath": "/uploads/report_1747055909855.pdf",
      "fileSize": "2.4 MB",
      "patient": {
        "_id": "6821f5111c121eb2864f1d61",
        "name": "MUHAMMAD ABDULLAH CHEEMA",
        "email": "<EMAIL>",
        "phone": "+92-***********",
        "age": 40,
        "gender": "male"
      },
      "doctor": {
        "_id": "68407db825ce90a8b1374f0d",
        "name": "Dr. Sarah Wilson",
        "email": "<EMAIL>",
        "specialization": "Cardiology"
      },
      "laboratory": {
        "name": "Central Medical Lab",
        "technician": "Lab Tech John",
        "equipment": "GE MAC 2000 ECG"
      },
      "priority": "routine",
      "tags": ["cardiac", "routine", "follow-up"],
      "createdAt": "2025-01-25T08:30:00.000Z",
      "updatedAt": "2025-01-25T14:22:00.000Z",
      "completedAt": null,
      "reviewedAt": null
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 15,
    "totalReports": 289,
    "hasNext": true,
    "hasPrev": false,
    "limit": 20
  },
  "filters": {
    "search": "blood",
    "status": "pending",
    "type": "Blood Test",
    "dateRange": {
      "from": "2025-01-01",
      "to": "2025-01-31"
    },
    "appliedFilters": 4
  },
  "summary": {
    "totalReports": 289,
    "pendingReports": 23,
    "completedReports": 245,
    "reviewedReports": 21,
    "averageCompletionTime": "2.3 days"
  }
}
```

#### 📊 GET `/api/doctor/reports/stats`
**Purpose:** Get comprehensive report statistics for dashboard and analytics

**Authentication:** Required (Doctor role)

**Response Structure:**
```json
{
  "success": true,
  "data": {
    "overview": {
      "total": 289,
      "pending": 23,
      "completed": 245,
      "reviewed": 21,
      "cancelled": 0
    },
    "timeBasedStats": {
      "today": 3,
      "thisWeek": 18,
      "thisMonth": 67,
      "lastMonth": 54
    },
    "typeBreakdown": [
      { "_id": "Blood Test", "count": 89, "percentage": 30.8 },
      { "_id": "X-Ray", "count": 67, "percentage": 23.2 },
      { "_id": "MRI Scan", "count": 45, "percentage": 15.6 },
      { "_id": "EKG/ECG", "count": 34, "percentage": 11.8 },
      { "_id": "Ultrasound", "count": 28, "percentage": 9.7 },
      { "_id": "CT Scan", "count": 26, "percentage": 9.0 }
    ],
    "statusTrends": {
      "completionRate": 84.8,
      "averageCompletionTime": "2.3 days",
      "pendingOlderThan7Days": 3,
      "reviewBacklog": 21
    },
    "recentActivity": [
      {
        "_id": "6821f5251c121eb2864f1d67",
        "type": "EKG/ECG",
        "patientName": "MUHAMMAD ABDULLAH CHEEMA",
        "date": "2025-01-25T00:00:00.000Z",
        "status": "pending",
        "priority": "routine",
        "daysOld": 4
      }
    ],
    "topPatients": [
      {
        "patientId": "6821f5111c121eb2864f1d61",
        "patientName": "MUHAMMAD ABDULLAH CHEEMA",
        "reportCount": 8,
        "lastReportDate": "2025-01-25T00:00:00.000Z"
      }
    ],
    "performanceMetrics": {
      "averageReportsPerDay": 3.2,
      "peakDay": "Tuesday",
      "mostCommonReportType": "Blood Test",
      "patientSatisfactionScore": 4.7
    }
  }
}
```

#### 🔍 GET `/api/doctor/reports/:reportId`
**Purpose:** Get detailed information about a specific medical report

**Authentication:** Required (Doctor role)
**Security:** Only returns reports created by the logged-in doctor

**Request Example:**
```http
GET /api/doctor/reports/6821f5251c121eb2864f1d67
Authorization: Bearer <jwt_token>
```

**Response Structure:**
```json
{
  "success": true,
  "data": {
    "_id": "6821f5251c121eb2864f1d67",
    "type": "EKG/ECG",
    "date": "2025-01-25T00:00:00.000Z",
    "status": "pending",
    "priority": "routine",
    "description": "Routine cardiac evaluation for chest pain symptoms reported by patient",
    "clinicalIndication": "Chest pain, rule out cardiac abnormalities",
    "findings": {
      "summary": "Normal sinus rhythm, no acute abnormalities detected",
      "details": [
        "Heart rate: 72 bpm",
        "PR interval: 160 ms (normal)",
        "QRS duration: 90 ms (normal)",
        "QT interval: 400 ms (normal)",
        "No ST-segment elevation or depression",
        "No T-wave abnormalities"
      ],
      "interpretation": "Normal ECG within normal limits"
    },
    "recommendations": [
      "Continue current medications",
      "Follow-up in 3 months",
      "Return if symptoms worsen",
      "Consider stress test if symptoms persist"
    ],
    "technicalDetails": {
      "equipment": "GE MAC 2000 ECG",
      "settings": "Standard 12-lead configuration",
      "filterSettings": "0.05-100 Hz",
      "paperSpeed": "25 mm/sec",
      "calibration": "10 mm/mV"
    },
    "pdfPath": "/uploads/report_1747055909855.pdf",
    "fileInfo": {
      "size": "2.4 MB",
      "pages": 3,
      "format": "PDF/A-1b",
      "created": "2025-01-25T08:30:00.000Z"
    },
    "patient": {
      "_id": "6821f5111c121eb2864f1d61",
      "name": "MUHAMMAD ABDULLAH CHEEMA",
      "email": "<EMAIL>",
      "phone": "+92-***********",
      "age": 40,
      "gender": "male",
      "medicalRecordNumber": "MRN-001234"
    },
    "doctor": {
      "_id": "68407db825ce90a8b1374f0d",
      "name": "Dr. Sarah Wilson",
      "email": "<EMAIL>",
      "specialization": "Cardiology",
      "licenseNumber": "MD-12345"
    },
    "laboratory": {
      "name": "Central Medical Lab",
      "address": "123 Medical Center Dr, Boston, MA",
      "technician": "Lab Tech John Smith",
      "certificationNumber": "LAB-CERT-789"
    },
    "workflow": {
      "ordered": "2025-01-24T14:00:00.000Z",
      "collected": "2025-01-25T08:00:00.000Z",
      "processed": "2025-01-25T08:30:00.000Z",
      "reported": "2025-01-25T09:00:00.000Z",
      "reviewed": null,
      "delivered": null
    },
    "qualityControl": {
      "verified": true,
      "verifiedBy": "QC Supervisor Jane Doe",
      "verificationDate": "2025-01-25T08:45:00.000Z",
      "qualityScore": 98.5
    },
    "tags": ["cardiac", "routine", "follow-up", "chest-pain"],
    "notes": [
      {
        "author": "Dr. Sarah Wilson",
        "timestamp": "2025-01-25T09:15:00.000Z",
        "content": "Patient reported mild chest discomfort during exercise. ECG shows normal findings."
      }
    ],
    "attachments": [
      {
        "type": "image",
        "filename": "ecg_strip_1.png",
        "path": "/uploads/attachments/ecg_strip_1.png",
        "description": "12-lead ECG strip"
      }
    ],
    "billing": {
      "cptCode": "93000",
      "description": "Electrocardiogram, routine ECG with at least 12 leads",
      "cost": 150.00,
      "insuranceCovered": true,
      "copay": 25.00
    },
    "createdAt": "2025-01-25T08:30:00.000Z",
    "updatedAt": "2025-01-25T14:22:00.000Z",
    "completedAt": null,
    "reviewedAt": null
  }
}
```

#### 📥 GET `/api/doctor/reports/:reportId/download`
**Purpose:** Download medical report PDF file with proper security and audit logging

**Authentication:** Required (Doctor role)
**Security:** Only allows download of reports created by the logged-in doctor

**Request Example:**
```http
GET /api/doctor/reports/6821f5251c121eb2864f1d67/download
Authorization: Bearer <jwt_token>
```

**Response Headers:**
```http
Content-Type: application/pdf
Content-Disposition: attachment; filename="CHEEMA_MUHAMMAD_ABDULLAH_EKG-ECG_2025-01-25.pdf"
Content-Length: 2516582
Cache-Control: private, no-cache
X-Download-Audit: logged
```

**Response:** Binary PDF file content

**Filename Format:** `{LastName}_{FirstName}_{ReportType}_{Date}.pdf`

**Error Responses:**
```json
// 404 Not Found
{
  "success": false,
  "message": "Report not found or access denied"
}

// 404 File Not Found
{
  "success": false,
  "message": "Report file not found on server"
}

// 403 Forbidden
{
  "success": false,
  "message": "Access denied. You can only download your own reports."
}
```

**Audit Logging:**
- Downloads are logged with timestamp, doctor ID, report ID, and IP address
- Compliance with HIPAA audit requirements
- Retention period: 7 years

#### 👁️ GET `/api/doctor/reports/:reportId/view`
**Purpose:** View medical report PDF in browser without downloading

**Authentication:** Required (Doctor role)
**Security:** Only allows viewing of reports created by the logged-in doctor

**Request Example:**
```http
GET /api/doctor/reports/6821f5251c121eb2864f1d67/view
Authorization: Bearer <jwt_token>
```

**Response Headers:**
```http
Content-Type: application/pdf
Content-Disposition: inline
Cache-Control: private, no-cache
X-Frame-Options: SAMEORIGIN
X-View-Audit: logged
```

**Response:** Binary PDF file content for inline viewing

**Security Features:**
- PDF opens in browser without download prompt
- Prevents unauthorized sharing through X-Frame-Options
- View actions are audit logged
- Session timeout applies to viewing

#### 📝 POST `/api/doctor/reports`
**Purpose:** Create a new medical report

**Authentication:** Required (Doctor role)

**Request Body:**
```json
{
  "patientId": "6821f5111c121eb2864f1d61",
  "type": "Blood Test",
  "date": "2025-01-29",
  "description": "Comprehensive metabolic panel for routine health screening",
  "clinicalIndication": "Annual physical examination, monitor diabetes",
  "priority": "routine",
  "tags": ["routine", "diabetes", "annual-physical"],
  "laboratoryId": "central-medical-lab",
  "expectedCompletionDate": "2025-01-31"
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Report created successfully",
  "data": {
    "_id": "new_report_id",
    "type": "Blood Test",
    "status": "pending",
    // ... complete report data
    "createdAt": "2025-01-29T10:30:00.000Z"
  }
}
```

#### ✏️ PUT `/api/doctor/reports/:reportId`
**Purpose:** Update an existing medical report

**Authentication:** Required (Doctor role)
**Security:** Only allows updating reports created by the logged-in doctor

**Request Body:** (All fields optional)
```json
{
  "status": "completed",
  "findings": {
    "summary": "All values within normal limits",
    "details": [
      "Glucose: 95 mg/dL (normal)",
      "Cholesterol: 180 mg/dL (normal)",
      "Triglycerides: 120 mg/dL (normal)"
    ]
  },
  "recommendations": [
    "Continue current diet and exercise regimen",
    "Recheck in 6 months"
  ],
  "completedAt": "2025-01-29T14:30:00.000Z"
}
```

#### 🗑️ DELETE `/api/doctor/reports/:reportId`
**Purpose:** Soft delete a medical report (marks as cancelled, doesn't remove from database)

**Authentication:** Required (Doctor role)
**Security:** Only allows deletion of reports created by the logged-in doctor

**Response:**
```json
{
  "success": true,
  "message": "Report cancelled successfully",
  "data": {
    "_id": "report_id",
    "status": "cancelled",
    "cancelledAt": "2025-01-29T15:00:00.000Z",
    "cancelledBy": "68407db825ce90a8b1374f0d"
  }
}
```

---

## 📅 Doctor Appointments APIs

### Base Route: `/api/doctor/appointments`

#### 📋 GET `/api/doctor/appointments`
**Purpose:** Get all appointments for the logged-in doctor with comprehensive filtering

**Authentication:** Required (Doctor role)

**Query Parameters:**
```http
GET /api/doctor/appointments?status=confirmed&date=2025-01-29&patientId=123&page=1&limit=20&sortBy=date&sortOrder=asc
```

**Available Query Parameters:**
- `status` (string): Filter by status (pending, confirmed, completed, cancelled, no-show)
- `date` (date): Filter by specific date (YYYY-MM-DD)
- `dateFrom` (date): Filter appointments from this date
- `dateTo` (date): Filter appointments until this date
- `patientId` (ObjectId): Filter appointments for specific patient
- `type` (string): Filter by appointment type (consultation, follow-up, emergency)
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10, max: 100)
- `sortBy` (string): Sort field (date, patientName, type, status)
- `sortOrder` (string): Sort direction (asc, desc, default: asc)

**Response Structure:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "appointment_id_123",
      "patient": {
        "_id": "6821f5111c121eb2864f1d61",
        "name": "MUHAMMAD ABDULLAH CHEEMA",
        "email": "<EMAIL>",
        "phone": "+92-***********",
        "age": 40,
        "gender": "male"
      },
      "doctor": {
        "_id": "68407db825ce90a8b1374f0d",
        "name": "Dr. Sarah Wilson",
        "email": "<EMAIL>",
        "specialization": "Cardiology"
      },
      "date": "2025-02-05T14:00:00.000Z",
      "endTime": "2025-02-05T14:30:00.000Z",
      "duration": 30,
      "type": "follow-up",
      "status": "confirmed",
      "reason": "Blood pressure follow-up and medication review",
      "symptoms": ["chest discomfort", "shortness of breath"],
      "notes": "Patient reports improvement since last visit",
      "location": {
        "room": "Cardiology Suite 3",
        "floor": "2nd Floor",
        "building": "Main Medical Building"
      },
      "priority": "routine",
      "isVirtual": false,
      "virtualMeetingLink": null,
      "reminders": {
        "patient": {
          "email": true,
          "sms": true,
          "sentAt": "2025-02-04T14:00:00.000Z"
        },
        "doctor": {
          "email": true,
          "sentAt": "2025-02-05T08:00:00.000Z"
        }
      },
      "insurance": {
        "provider": "State Life Insurance",
        "policyNumber": "SLI-123456789",
        "copay": 25.00,
        "preauthorized": true
      },
      "billing": {
        "estimatedCost": 150.00,
        "actualCost": null,
        "paid": false,
        "paymentMethod": null
      },
      "createdAt": "2025-01-20T10:30:00.000Z",
      "updatedAt": "2025-01-28T16:45:00.000Z",
      "createdBy": "patient",
      "lastModifiedBy": "doctor"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 8,
    "totalAppointments": 156,
    "hasNext": true,
    "hasPrev": false,
    "limit": 20
  },
  "summary": {
    "today": 8,
    "thisWeek": 45,
    "pending": 12,
    "confirmed": 89,
    "completed": 298,
    "cancelled": 32,
    "noShow": 5
  }
}
```

#### 📊 GET `/api/doctor/appointments/stats`
**Purpose:** Get appointment statistics for dashboard

**Response Structure:**
```json
{
  "success": true,
  "data": {
    "overview": {
      "total": 342,
      "today": 8,
      "thisWeek": 45,
      "thisMonth": 156,
      "pending": 12,
      "confirmed": 89,
      "completed": 298,
      "cancelled": 32,
      "noShow": 5
    },
    "timeSlots": {
      "morning": { "count": 145, "percentage": 42.4 },
      "afternoon": { "count": 134, "percentage": 39.2 },
      "evening": { "count": 63, "percentage": 18.4 }
    },
    "appointmentTypes": [
      { "type": "consultation", "count": 156, "percentage": 45.6 },
      { "type": "follow-up", "count": 134, "percentage": 39.2 },
      { "type": "emergency", "count": 52, "percentage": 15.2 }
    ],
    "weeklyTrends": [
      { "day": "Monday", "count": 52 },
      { "day": "Tuesday", "count": 67 },
      { "day": "Wednesday", "count": 58 },
      { "day": "Thursday", "count": 61 },
      { "day": "Friday", "count": 49 },
      { "day": "Saturday", "count": 32 },
      { "day": "Sunday", "count": 23 }
    ],
    "upcomingToday": [
      {
        "_id": "appointment_id",
        "patientName": "MUHAMMAD ABDULLAH CHEEMA",
        "time": "14:00",
        "type": "follow-up",
        "status": "confirmed"
      }
    ],
    "performance": {
      "averageDuration": "28 minutes",
      "onTimePercentage": 87.5,
      "cancellationRate": 9.4,
      "noShowRate": 1.5
    }
  }
}
```

#### 🔍 GET `/api/doctor/appointments/:appointmentId`
**Purpose:** Get detailed information about a specific appointment

**Response Structure:**
```json
{
  "success": true,
  "data": {
    "_id": "appointment_id_123",
    // Complete appointment details (same structure as list response)
    "history": [
      {
        "action": "created",
        "timestamp": "2025-01-20T10:30:00.000Z",
        "by": "patient",
        "details": "Appointment requested by patient"
      },
      {
        "action": "confirmed",
        "timestamp": "2025-01-20T11:15:00.000Z",
        "by": "doctor",
        "details": "Appointment confirmed by Dr. Sarah Wilson"
      }
    ],
    "relatedRecords": {
      "previousAppointments": 3,
      "lastAppointment": "2024-11-15T14:00:00.000Z",
      "relatedReports": 2,
      "prescriptions": 1
    }
  }
}
```

#### ✏️ PUT `/api/doctor/appointments/:appointmentId`
**Purpose:** Update appointment details (doctor can modify status, notes, etc.)

**Request Body:**
```json
{
  "status": "completed",
  "notes": "Patient showed significant improvement. Continue current medication.",
  "actualDuration": 35,
  "followUpRequired": true,
  "followUpDate": "2025-03-05T14:00:00.000Z",
  "prescriptions": [
    {
      "medication": "Lisinopril",
      "dosage": "10mg",
      "frequency": "once daily",
      "duration": "30 days"
    }
  ]
}
```

#### 📝 POST `/api/doctor/appointments`
**Purpose:** Create a new appointment (doctor-initiated)

**Request Body:**
```json
{
  "patientId": "6821f5111c121eb2864f1d61",
  "date": "2025-02-10T15:00:00.000Z",
  "duration": 30,
  "type": "follow-up",
  "reason": "Medication review and blood pressure check",
  "priority": "routine",
  "location": {
    "room": "Cardiology Suite 3",
    "floor": "2nd Floor"
  }
}
```

---

## 📁 File Management APIs

### Base Route: `/api/doctor/files`

#### 📤 POST `/api/doctor/files/upload`
**Purpose:** Upload medical files (reports, images, documents)

**Authentication:** Required (Doctor role)

**Request:** Multipart form data
```http
POST /api/doctor/files/upload
Authorization: Bearer <jwt_token>
Content-Type: multipart/form-data

Form Data:
- file: [PDF/Image file]
- patientId: "6821f5111c121eb2864f1d61"
- type: "report" | "image" | "document"
- description: "EKG report for chest pain evaluation"
- category: "cardiology"
```

**Response:**
```json
{
  "success": true,
  "message": "File uploaded successfully",
  "data": {
    "_id": "file_id_123",
    "filename": "ekg_report_20250129.pdf",
    "originalName": "patient_ekg.pdf",
    "path": "/uploads/reports/ekg_report_20250129.pdf",
    "size": 2516582,
    "mimeType": "application/pdf",
    "type": "report",
    "description": "EKG report for chest pain evaluation",
    "category": "cardiology",
    "patientId": "6821f5111c121eb2864f1d61",
    "uploadedBy": "68407db825ce90a8b1374f0d",
    "uploadedAt": "2025-01-29T10:30:00.000Z",
    "virus_scan": {
      "status": "clean",
      "scannedAt": "2025-01-29T10:30:15.000Z"
    }
  }
}
```

#### 📋 GET `/api/doctor/files`
**Purpose:** Get all files uploaded by the doctor

**Query Parameters:**
- `patientId` - Filter by patient
- `type` - Filter by file type
- `category` - Filter by category
- `page`, `limit` - Pagination

#### 🗑️ DELETE `/api/doctor/files/:fileId`
**Purpose:** Delete an uploaded file

---

## 🗄️ Database Models & Relationships

### User Model (models/User.js)
```javascript
{
  _id: ObjectId,
  name: String,
  email: String (unique),
  password: String (hashed),
  role: String (enum: ['doctor', 'patient', 'admin']),
  profile: {
    specialization: String, // for doctors
    experience: String,     // for doctors
    phone: String,
    address: Object
  },
  createdAt: Date,
  updatedAt: Date
}
```

### PatientRecord Model (models/doctor/PatientRecord.js)
```javascript
{
  _id: ObjectId,
  name: String,
  email: String,
  phone: String,
  age: Number,
  gender: String,
  dateOfBirth: Date,
  address: {
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: String
  },
  medicalHistory: {
    bloodType: String,
    height: String,
    weight: String,
    allergies: [String],
    chronicConditions: [String],
    medications: [{
      name: String,
      dosage: String,
      frequency: String,
      startDate: Date
    }]
  },
  emergencyContact: {
    name: String,
    relationship: String,
    phone: String,
    email: String
  },
  insurance: {
    provider: String,
    policyNumber: String,
    groupNumber: String
  },
  createdAt: Date,
  updatedAt: Date
}
```

### Report Model (models/doctor/Report.js)
```javascript
{
  _id: ObjectId,
  type: String,
  date: Date,
  status: String (enum: ['pending', 'completed', 'reviewed', 'cancelled']),
  description: String,
  findings: {
    summary: String,
    details: [String],
    interpretation: String
  },
  recommendations: [String],
  pdfPath: String,
  patient: ObjectId (ref: 'PatientRecord'),
  doctor: ObjectId (ref: 'User'),
  laboratory: {
    name: String,
    technician: String,
    equipment: String
  },
  priority: String (enum: ['routine', 'urgent', 'stat']),
  tags: [String],
  createdAt: Date,
  updatedAt: Date,
  completedAt: Date,
  reviewedAt: Date
}
```

### Appointment Model (models/Appointment.js)
```javascript
{
  _id: ObjectId,
  patient: ObjectId (ref: 'User'),
  doctor: ObjectId (ref: 'User'),
  date: Date,
  endTime: Date,
  duration: Number, // minutes
  type: String (enum: ['consultation', 'follow-up', 'emergency']),
  status: String (enum: ['pending', 'confirmed', 'completed', 'cancelled', 'no-show']),
  reason: String,
  symptoms: [String],
  notes: String,
  location: {
    room: String,
    floor: String,
    building: String
  },
  priority: String,
  isVirtual: Boolean,
  virtualMeetingLink: String,
  createdAt: Date,
  updatedAt: Date
}
```

### Relationships
```
User (Doctor) ──┬── Report (doctor field)
                ├── Appointment (doctor field)
                └── PatientRecord (created by doctor)

PatientRecord ──┬── Report (patient field)
                └── Appointment (patient field via User)

Report ────────── PatientRecord (patient field)
Appointment ───── User (patient & doctor fields)
```
