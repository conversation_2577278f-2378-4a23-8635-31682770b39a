{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-openid-connect": "^2.18.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "multer": "^1.4.2", "multer-gridfs-storage": "^5.0.2", "pdfkit": "^0.17.1"}, "devDependencies": {"nodemon": "^3.1.10"}}