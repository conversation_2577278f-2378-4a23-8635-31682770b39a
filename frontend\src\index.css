
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 210 79% 51%;
    --primary-foreground: 210 40% 98%;

    --secondary: 122 39% 49%;
    --secondary-foreground: 210 40% 98%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 16 100% 57%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 222 84% 5%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 240 5% 26%;
    --sidebar-primary: 210 79% 51%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 5% 96%;
    --sidebar-accent-foreground: 240 6% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217 91% 60%;
  }

  .dark {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 79% 51%;
    --primary-foreground: 210 40% 98%;

    --secondary: 122 39% 49%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 18%;
    --muted-foreground: 215 20% 65%;

    --accent: 16 100% 57%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 18%;
    --input: 217 33% 18%;
    --ring: 213 27% 84%;

    --sidebar-background: 240 6% 10%;
    --sidebar-foreground: 240 5% 96%;
    --sidebar-primary: 210 79% 51%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 4% 16%;
    --sidebar-accent-foreground: 240 5% 96%;
    --sidebar-border: 240 4% 16%;
    --sidebar-ring: 217 91% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  .healwise-card {
    @apply bg-white rounded-xl shadow-md p-6 border border-border transition-all hover:shadow-lg;
  }
  
  .healwise-gradient {
    @apply bg-gradient-to-r from-healwise-blue to-healwise-green text-white;
  }
  
  .healwise-input {
    @apply rounded-lg border-input bg-transparent px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }
}
